<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.4.5</version>
    <relativePath/> <!-- lookup parent from repository -->
  </parent>

  <groupId>com.seera.lumi.core</groupId>
  <artifactId>integrations-adapter</artifactId>
  <version>1.0.0</version>
  <name>integrations-adapter</name>
  <description>An Adapter service for the communication with external systems.</description>

  <properties>
    <java.version>21</java.version>
    <compiler-plugin.version>3.13.0</compiler-plugin.version>
    <spring-cloud.version>2024.0.1</spring-cloud.version>
    <org.mapstruct.version>1.6.3</org.mapstruct.version>
    <keycloak.core.version>26.1.5</keycloak.core.version>
    <keycloak.version>26.0.5</keycloak.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <!-- START DATABASE DEP -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>io.hypersistence</groupId>
      <artifactId>hypersistence-utils-hibernate-63</artifactId>
      <version>3.9.0</version>
    </dependency>
    <dependency>
      <groupId>org.flywaydb</groupId>
      <artifactId>flyway-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.flywaydb</groupId>
      <artifactId>flyway-mysql</artifactId>
    </dependency>
    <!-- END DATABASE DEP -->

    <!-- START LUMI CORE DEPS-->
    <dependency>
      <groupId>com.seera.lumi.core</groupId>
      <artifactId>lumi-core-swagger</artifactId>
      <version>0.0.1</version>
    </dependency>
    <dependency>
      <groupId>com.seera.lumi.core</groupId>
      <artifactId>lumi-core-caching</artifactId>
      <version>0.0.2</version>
    </dependency>
    <dependency>
      <groupId>com.seera.lumi.core</groupId>
      <artifactId>lumi-core-date-util</artifactId>
      <version>0.0.1</version>
    </dependency>
    <dependency>
      <groupId>com.seera.lumi.core</groupId>
      <artifactId>lumi-core-security</artifactId>
      <version>0.0.4</version>
    </dependency>
    <dependency>
      <groupId>com.seera.lumi.core</groupId>
      <artifactId>lumi-core-logging</artifactId>
      <version>0.0.4</version>
    </dependency>
    <!-- END LUMI CORE DEPS -->

    <!-- START MESSAGING DEP -->
    <dependency>
      <groupId>org.springframework.kafka</groupId>
      <artifactId>spring-kafka</artifactId>
    </dependency>
    <!-- END MESSAGING DEP  -->

    <!--Security Dependencies START-->
    <dependency>
      <groupId>org.keycloak</groupId>
      <artifactId>keycloak-core</artifactId>
      <version>${keycloak.core.version}</version>
    </dependency>
    <dependency>
      <groupId>org.keycloak</groupId>
      <artifactId>keycloak-policy-enforcer</artifactId>
      <version>${keycloak.version}</version>
    </dependency>
    <!--Security Dependencies END-->

    <!-- START Openfeign DEP -->
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-openfeign</artifactId>
    </dependency>
    <!-- Just Removed the vulnerabilities-->
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.18.0</version>
    </dependency>
    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-okhttp</artifactId>
    </dependency>
    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-micrometer</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-oauth2-client</artifactId>
    </dependency>
    <!-- END Openfeign DEP -->

    <!-- START Monitoring Dep -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-registry-prometheus</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-tracing-bridge-brave</artifactId>
    </dependency>
    <dependency>
      <groupId>com.newrelic.agent.java</groupId>
      <artifactId>newrelic-java</artifactId>
      <version>8.18.0</version>
      <scope>provided</scope>
      <type>zip</type>
    </dependency>
    <dependency>
      <groupId>com.newrelic.agent.java</groupId>
      <artifactId>newrelic-api</artifactId>
      <version>8.18.0</version>
    </dependency>
    <!-- END Monitoring Dep -->

    <!-- START ANNOTATION PROCESSOR DEP -->
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
      <version>${org.mapstruct.version}</version>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct-processor</artifactId>
      <version>${org.mapstruct.version}</version>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${lombok.version}</version>
      <scope>provided</scope>
    </dependency>
    <!-- END ANNOTATION PROCESSOR DEP -->

    <!--	WSDL generation dependencies Start -->
    <dependency>
      <groupId>jakarta.xml.bind</groupId>
      <artifactId>jakarta.xml.bind-api</artifactId>
      <version>4.0.1</version>
    </dependency>
    <dependency>
      <groupId>javax.xml.bind</groupId>
      <artifactId>jaxb-api</artifactId>
      <version>2.3.1</version>
    </dependency>
    <dependency>
      <groupId>jakarta.xml.ws</groupId>
      <artifactId>jakarta.xml.ws-api</artifactId>
      <version>4.0.1</version>
    </dependency>
    <dependency>
      <groupId>jakarta.jws</groupId>
      <artifactId>jakarta.jws-api</artifactId>
      <version>3.0.0</version>
    </dependency>
    <dependency>
      <groupId>jakarta.xml.soap</groupId>
      <artifactId>jakarta.xml.soap-api</artifactId>
      <version>3.0.1</version>
    </dependency>

    <dependency>
      <groupId>org.glassfish.hk2</groupId>
      <artifactId>hk2-api</artifactId>
      <version>3.0.5</version>
    </dependency>

    <dependency>
      <groupId>jakarta.activation</groupId>
      <artifactId>jakarta.activation-api</artifactId>
      <version>2.1.2</version>
    </dependency>
    <dependency>
      <groupId>com.sun.xml.ws</groupId>
      <artifactId>jaxws-rt</artifactId>
      <version>4.0.2</version>
      <scope>runtime</scope>
    </dependency>

    <dependency>
      <groupId>com.sun.xml.messaging.saaj</groupId>
      <artifactId>saaj-impl</artifactId>
      <version>3.0.3</version>
    </dependency>
    <!--	WSDL generation dependencies END -->
  </dependencies>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>${spring-cloud.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-tracing-bom</artifactId>
        <version>${micrometer-tracing.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <compilerArgs>
            <arg>-parameters</arg>
          </compilerArgs>
          <source>${java.version}</source>
          <target>${java.version}</target>
          <showDeprecation>true</showDeprecation>
          <annotationProcessorPaths>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
            <path>
              <groupId>org.mapstruct</groupId>
              <artifactId>mapstruct-processor</artifactId>
              <version>${org.mapstruct.version}</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.cxf</groupId>
        <artifactId>cxf-codegen-plugin</artifactId>
        <version>4.0.3</version>
        <executions>
          <execution>
            <id>generate-sources</id>
            <phase>generate-sources</phase>
            <configuration>
              <sourceRoot>${project.build.directory}/generated-sources</sourceRoot>
              <wsdlOptions>
                <wsdlOption>
                  <wsdl>src/main/resources/wsdl/sap/FetchLumiVehicleInformation_OutService.wsdl
                  </wsdl>
                </wsdlOption>
                <wsdlOption>
                  <wsdl>src/main/resources/wsdl/sap/FetchLumiCustomerMasterDetails_OutService.wsdl
                  </wsdl>
                </wsdlOption>
                <wsdlOption>
                  <wsdl>src/main/resources/wsdl/sap/LumiCustomerMaster_OutServiceupdated.wsdl</wsdl>
                </wsdlOption>
                <wsdlOption>
                  <wsdl>src/main/resources/wsdl/sap/CustomerPaymentPosting_OutService.wsdl</wsdl>
                </wsdlOption>
                <wsdlOption>
                  <wsdl>src/main/resources/wsdl/sap/LumiRentalSalesOrder_OutService_340.wsdl</wsdl>
                </wsdlOption>
                <wsdlOption>
                  <wsdl>src/main/resources/wsdl/sap/FixedAssetShutdownService340.wsdl</wsdl>
                </wsdlOption>
                <wsdlOption>
                  <wsdl>src/main/resources/wsdl/sap/LeaseQuotation_OutService340.wsdl</wsdl>
                </wsdlOption>
              </wsdlOptions>
            </configuration>
            <goals>
              <goal>wsdl2java</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <!-- Unzip New Relic Java agent into target/ -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <version>3.1.1</version>
        <executions>
          <execution>
            <id>unpack-newrelic</id>
            <phase>package</phase>
            <goals>
              <goal>unpack-dependencies</goal>
            </goals>
            <configuration>
              <includeGroupIds>com.newrelic.agent.java</includeGroupIds>
              <includeArtifactIds>newrelic-java</includeArtifactIds>
              <!-- you can optionally exclude files -->
              <excludes>**/newrelic.yml</excludes>
              <overWriteReleases>false</overWriteReleases>
              <overWriteSnapshots>false</overWriteSnapshots>
              <overWriteIfNewer>true</overWriteIfNewer>
              <outputDirectory>${project.build.directory}</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <repositories>
    <repository>
      <id>lumi-artifactory</id>
      <name>libs-release</name>
      <url>https://artifactory.lumirental.io/artifactory/libs-release-local</url>
    </repository>
    <repository>
      <id>central</id>
      <name>Maven Central Repository</name>
      <url>https://repo1.maven.org/maven2</url>
    </repository>
  </repositories>
</project>
