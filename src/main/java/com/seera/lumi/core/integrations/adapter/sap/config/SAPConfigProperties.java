package com.seera.lumi.core.integrations.adapter.sap.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "integration.client.sap")
public class SAPConfigProperties {

  private Interface interfaces;
  private String username;
  private String password;

  @Getter
  @Setter
  public static class Interface {
    private String fetchVehicleInfo;
    private String fetchCustomerInfo;
    private String createCustomer;
    private String createCustomerPayment;
    private String createSalesOrder;
    private String fixedAssetShutdown;
    private String leaseQuotation;
  }
}
