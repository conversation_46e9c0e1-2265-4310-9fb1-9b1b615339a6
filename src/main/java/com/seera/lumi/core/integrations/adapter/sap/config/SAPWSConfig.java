package com.seera.lumi.core.integrations.adapter.sap.config;

import com.altayyargroup.fin.documentposting.DocumentPostingOut;
import com.altayyargroup.fin.documentposting.DocumentPostingOutService;
import com.altayyargroup.fin.fetchlumivehicleinformation.FetchLumiVehicleInformationOut;
import com.altayyargroup.fin.fetchlumivehicleinformation.FetchLumiVehicleInformationOutService;
import com.altayyargroup.mdm.fetchlumicustomermasterdetails.FetchLumiCustomerMasterDetailsOut;
import com.altayyargroup.mdm.fetchlumicustomermasterdetails.FetchLumiCustomerMasterDetailsOutService;
import com.altayyargroup.mdm.lumicustomermaster.LumiCustomerMasterOut;
import com.altayyargroup.mdm.lumicustomermaster.LumiCustomerMasterOutService;
import com.altayyargroup.scm.salesorder.SalesOrderOut;
import com.altayyargroup.scm.salesorder.SalesOrderOutService;
import com.altayyargroup.fin.assetshutdown.FixedAssetShutdown;
import com.altayyargroup.fin.assetshutdown.FixedAssetShutdownService;
import com.altayyargroup.scm.leasequotation.LeaseQuotationOut;
import com.altayyargroup.scm.leasequotation.LeaseQuotationOutService;
import jakarta.xml.ws.BindingProvider;
import java.io.IOException;
import java.util.Map;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

@Configuration
public class SAPWSConfig {

  private static final String CONNECT_TIMEOUT = "com.sun.xml.ws.connect.timeout";
  private static final String READ_TIMEOUT = "com.sun.xml.ws.request.timeout";

  private static final Integer DEFAULT_TIMEOUT = 3000;
  private static final String FETCH_VEHICLE_INFO_WSDL_PATH =
      "wsdl/sap/FetchLumiVehicleInformation_OutService.wsdl";
  private static final String FETCH_CUSTOMER_INFO_WSDL_PATH =
      "wsdl/sap/FetchLumiCustomerMasterDetails_OutService.wsdl";
  private static final String CUSTOMER_MASTER_DATA_WSDL_PATH =
      "wsdl/sap/LumiCustomerMaster_OutServiceupdated.wsdl";
  private static final String DOCUMENT_POSTING_WSDL_PATH =
      "wsdl/sap/CustomerPaymentPosting_OutService.wsdl";
  private static final String SALES_ORDER_WSDL_PATH =
      "wsdl/sap/LumiRentalSalesOrder_OutService_340.wsdl";
  private static final String FIXED_ASSET_SHUTDOWN_WSDL_PATH =
      "wsdl/sap/FixedAssetShutdownService340.wsdl";
  private static final String LEASE_QUOTATION_WSDL_PATH =
      "wsdl/sap/LeaseQuotation_OutService340.wsdl";

  @Bean
  public FetchLumiVehicleInformationOut sapVehicleInformationWebService(
      SAPConfigProperties sapConfigProperties) throws IOException {
    FetchLumiVehicleInformationOut vehicleInfoService =
        new FetchLumiVehicleInformationOutService(
                new ClassPathResource(FETCH_VEHICLE_INFO_WSDL_PATH).getURL())
            .getHTTPPort();
    BindingProvider provider = (BindingProvider) vehicleInfoService;
    Map<String, Object> requestContext = provider.getRequestContext();
    requestContext.put(
        BindingProvider.ENDPOINT_ADDRESS_PROPERTY,
        sapConfigProperties.getInterfaces().getFetchVehicleInfo());
    configureRequestContext(requestContext, sapConfigProperties);
    return vehicleInfoService;
  }

  @Bean
  public FetchLumiCustomerMasterDetailsOut sapCustomerWebService(
      SAPConfigProperties sapConfigProperties) throws IOException {
    FetchLumiCustomerMasterDetailsOut customerInfoService =
        new FetchLumiCustomerMasterDetailsOutService(
                new ClassPathResource(FETCH_CUSTOMER_INFO_WSDL_PATH).getURL())
            .getHTTPPort();
    BindingProvider provider = (BindingProvider) customerInfoService;
    Map<String, Object> requestContext = provider.getRequestContext();
    requestContext.put(
        BindingProvider.ENDPOINT_ADDRESS_PROPERTY,
        sapConfigProperties.getInterfaces().getFetchCustomerInfo());
    configureRequestContext(requestContext, sapConfigProperties);
    return customerInfoService;
  }

  @Bean
  public LumiCustomerMasterOut sapCustomerMasterDataService(SAPConfigProperties sapConfigProperties)
      throws IOException {
    LumiCustomerMasterOut customerMasterDataService =
        new LumiCustomerMasterOutService(
                new ClassPathResource(CUSTOMER_MASTER_DATA_WSDL_PATH).getURL())
            .getHTTPPort();
    BindingProvider provider = (BindingProvider) customerMasterDataService;
    Map<String, Object> requestContext = provider.getRequestContext();
    requestContext.put(
        BindingProvider.ENDPOINT_ADDRESS_PROPERTY,
        sapConfigProperties.getInterfaces().getCreateCustomer());
    configureRequestContext(requestContext, sapConfigProperties);
    return customerMasterDataService;
  }

  @Bean
  public DocumentPostingOut customerPaymentService(SAPConfigProperties sapConfigProperties)
      throws IOException {
    DocumentPostingOut documentPostingOut =
        new DocumentPostingOutService(new ClassPathResource(DOCUMENT_POSTING_WSDL_PATH).getURL())
            .getHTTPPort();
    BindingProvider provider = (BindingProvider) documentPostingOut;
    Map<String, Object> requestContext = provider.getRequestContext();
    requestContext.put(
        BindingProvider.ENDPOINT_ADDRESS_PROPERTY,
        sapConfigProperties.getInterfaces().getCreateCustomerPayment());
    configureRequestContext(requestContext, sapConfigProperties);
    return documentPostingOut;
  }

  @Bean
  public SalesOrderOut salesOrderService(SAPConfigProperties sapConfigProperties)
      throws IOException {
    SalesOrderOut salesOrderOut =
        new SalesOrderOutService(new ClassPathResource(SALES_ORDER_WSDL_PATH).getURL())
            .getHTTPPort();
    BindingProvider provider = (BindingProvider) salesOrderOut;
    Map<String, Object> requestContext = provider.getRequestContext();
    requestContext.put(
        BindingProvider.ENDPOINT_ADDRESS_PROPERTY,
        sapConfigProperties.getInterfaces().getCreateSalesOrder());
    configureRequestContext(requestContext, sapConfigProperties);
    return salesOrderOut;
  }

  @Bean
  public FixedAssetShutdown fixedAssetShutdownService(SAPConfigProperties sapConfigProperties)
      throws IOException {
    FixedAssetShutdown fixedAssetShutdown =
        new FixedAssetShutdownService(new ClassPathResource(FIXED_ASSET_SHUTDOWN_WSDL_PATH).getURL())
            .getHTTPPort();
    BindingProvider provider = (BindingProvider) fixedAssetShutdown;
    Map<String, Object> requestContext = provider.getRequestContext();
    requestContext.put(
        BindingProvider.ENDPOINT_ADDRESS_PROPERTY,
        sapConfigProperties.getInterfaces().getFixedAssetShutdown());
    configureRequestContext(requestContext, sapConfigProperties);
    return fixedAssetShutdown;
  }

  @Bean
  public LeaseQuotationOut leaseQuotationService(SAPConfigProperties sapConfigProperties)
      throws IOException {
    LeaseQuotationOut leaseQuotationOut =
        new LeaseQuotationOutService(new ClassPathResource(LEASE_QUOTATION_WSDL_PATH).getURL())
            .getHTTPPort();
    BindingProvider provider = (BindingProvider) leaseQuotationOut;
    Map<String, Object> requestContext = provider.getRequestContext();
    requestContext.put(
        BindingProvider.ENDPOINT_ADDRESS_PROPERTY,
        sapConfigProperties.getInterfaces().getLeaseQuotation());
    configureRequestContext(requestContext, sapConfigProperties);
    return leaseQuotationOut;
  }

  public void configureRequestContext(
      Map<String, Object> requestContext, SAPConfigProperties sapConfigProperties) {
    requestContext.put(BindingProvider.USERNAME_PROPERTY, sapConfigProperties.getUsername());
    requestContext.put(BindingProvider.PASSWORD_PROPERTY, sapConfigProperties.getPassword());
    requestContext.put(CONNECT_TIMEOUT, DEFAULT_TIMEOUT);
    requestContext.put(READ_TIMEOUT, DEFAULT_TIMEOUT);
  }
}
