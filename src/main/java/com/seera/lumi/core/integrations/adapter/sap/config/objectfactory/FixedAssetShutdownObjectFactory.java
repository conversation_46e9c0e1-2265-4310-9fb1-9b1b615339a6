package com.seera.lumi.core.integrations.adapter.sap.config.objectfactory;

import com.altayyargroup.fin.assetshutdown.FixedAssetShutdown_Type;
import com.altayyargroup.fin.assetshutdown.ObjectFactory;
import com.seera.lumi.core.integrations.adapter.sap.dto.request.FixedAssetShutdownRequestDTO;
import org.springframework.stereotype.Component;

@Component
public class FixedAssetShutdownObjectFactory extends ObjectFactory {

  public FixedAssetShutdown_Type create(FixedAssetShutdownRequestDTO request) {
    FixedAssetShutdown_Type fixedAssetShutdown = createFixedAssetShutdown_Type();
    FixedAssetShutdown_Type.Record shutdownRecord = createFixedAssetShutdown_TypeRecord();

    shutdownRecord.setCOMPANYCODE(request.getCompanyCode());
    shutdownRecord.setASSET(request.getAsset());
    shutdownRecord.setSUBNUMBER(request.getSubNumber());
    shutdownRecord.setFROMDATE(request.getFromDate());
    shutdownRecord.setSHUTDOWN(request.getShutdown());
    fixedAssetShutdown.getRecord().add(shutdownRecord);
    return fixedAssetShutdown;
  }
}
