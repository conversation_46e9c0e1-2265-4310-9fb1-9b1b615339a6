package com.seera.lumi.core.integrations.adapter.sap.config.objectfactory;

import com.altayyargroup.scm.leasequotation.LeaseQuotation;
import com.altayyargroup.scm.leasequotation.ObjectFactory;
import com.seera.lumi.core.integrations.adapter.sap.dto.request.LeaseQuotationRequestDTO;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class LeaseQuotationObjectFactory extends ObjectFactory {

  public LeaseQuotation create(LeaseQuotationRequestDTO request) {
    LeaseQuotation leaseQuotation = createLeaseQuotation();

    // Iterate through each record in the request DTO
    if (!CollectionUtils.isEmpty(request.getRecord())) {
      for (LeaseQuotationRequestDTO.Record requestRecord : request.getRecord()) {
        LeaseQuotation.Record quotationRecord = createLeaseQuotationRecord();

        // Map Header
        if (requestRecord.getHeader() != null) {
          LeaseQuotation.Record.Header quotationHeader = createLeaseQuotationRecordHeader();
          mapHeaderFields(requestRecord.getHeader(), quotationHeader);
          quotationRecord.setHeader(quotationHeader);
        }

        // Map Detail Items
        if (!CollectionUtils.isEmpty(requestRecord.getDetail())) {
          for (LeaseQuotationRequestDTO.Detail requestDetail : requestRecord.getDetail()) {
            LeaseQuotation.Record.Detail quotationDetail = createLeaseQuotationRecordDetail();
            mapDetailFields(requestDetail, quotationDetail);
            quotationRecord.getDetail().add(quotationDetail);
          }
        }

        leaseQuotation.getRecord().add(quotationRecord);
      }
    }

    return leaseQuotation;
  }

  private void mapHeaderFields(
      LeaseQuotationRequestDTO.Header source, LeaseQuotation.Record.Header target) {
    if (source == null) return;

    if (source.getLeaseQuotationNumber() != null) {
      target.setLeaseQuotationNumber(source.getLeaseQuotationNumber());
    }

    if (source.getGroupNumber() != null) {
      target.setGroupNumber(source.getGroupNumber());
    }

    if (source.getSerialNumber() != null) {
      target.setSerialNumber(source.getSerialNumber());
    }
  }

  private void mapDetailFields(
      LeaseQuotationRequestDTO.Detail source, LeaseQuotation.Record.Detail target) {
    if (source == null) return;

    target.setMake(source.getMake());
    target.setModel(source.getModel());
    target.setVersion(source.getVersion());
    target.setSeries(source.getSeries());
    target.setCarGroup(source.getCarGroup());
    target.setVehicleCode(source.getVehicleCode());
    target.setGroupAccessoryCode(source.getGroupAccessoryCode());
    target.setAccessoryCode(source.getAccessoryCode());
    target.setAccessoryType(source.getAccessoryType());
    target.setOtherItemsCode(source.getOtherItemsCode());
    target.setVehiclePrice(source.getVehiclePrice());
    target.setAccessoryPrice(source.getAccessoryPrice());
    target.setCurrency(source.getCurrency());
    target.setQuantity(source.getQuantity());
    target.setBranch(source.getBranch());
    target.setCompanyCode(source.getCompanyCode());
    target.setCreationDate(source.getCreationDate());
    target.setDebitor(source.getDebitor());
    target.setValidationDate(source.getValidationDate());
    target.setExpectedStartDateofLease(source.getExpectedStartDateofLease());
    target.setSupplier(source.getSupplier());
    target.setCustomer(source.getCustomer());
    target.setDurationMonths(source.getDurationMonths());
    target.setDurationKms(source.getDurationKms());
    target.setCreatedBy(source.getCreatedBy());
    target.setRequisitioner(source.getRequisitioner());
    target.setModelYear(source.getModelYear());
  }
}
