package com.seera.lumi.core.integrations.adapter.sap.controller;

import com.altayyargroup.fin.assetshutdown.FixedAssetShutdown_Type;
import com.seera.lumi.core.integrations.adapter.sap.config.objectfactory.FixedAssetShutdownObjectFactory;
import com.seera.lumi.core.integrations.adapter.sap.dto.request.FixedAssetShutdownRequestDTO;
import com.seera.lumi.core.integrations.adapter.sap.service.impl.FixedAssetShutdownProxyService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/sap/asset/status")
@RequiredArgsConstructor
public class SAPFixedAssetShutdownController {

  private final FixedAssetShutdownObjectFactory fixedAssetShutdownObjectFactory;
  private final FixedAssetShutdownProxyService fixedAssetShutdownProxyService;

  @PostMapping
  public ResponseEntity<?> shutdownFixedAsset(
      @Valid @RequestBody FixedAssetShutdownRequestDTO fixedAssetShutdownRequest) {
    try {
      FixedAssetShutdown_Type fixedAssetShutdown =
          fixedAssetShutdownObjectFactory.create(fixedAssetShutdownRequest);
      fixedAssetShutdownProxyService.fixedAssetShutdown(fixedAssetShutdown);
      return ResponseEntity.ok().build();
    } catch (Exception ex) {
      log.error("Error while processing SAP Fixed Asset Shutdown ", ex);
      return ResponseEntity.internalServerError().body(ex.getMessage());
    }
  }
}
