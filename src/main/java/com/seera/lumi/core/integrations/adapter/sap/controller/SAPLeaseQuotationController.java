package com.seera.lumi.core.integrations.adapter.sap.controller;

import com.altayyargroup.scm.leasequotation.LeaseQuotation;
import com.seera.lumi.core.integrations.adapter.sap.config.objectfactory.LeaseQuotationObjectFactory;
import com.seera.lumi.core.integrations.adapter.sap.dto.request.LeaseQuotationRequestDTO;
import com.seera.lumi.core.integrations.adapter.sap.dto.response.LeaseQuotationResponseDTO;
import com.seera.lumi.core.integrations.adapter.sap.service.impl.LeaseQuotationOutProxyService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/sap/lease-quotation")
@RequiredArgsConstructor
public class SAPLeaseQuotationController {

  private final LeaseQuotationObjectFactory leaseQuotationObjectFactory;
  private final LeaseQuotationOutProxyService leaseQuotationOutProxyService;

  @PostMapping
  public ResponseEntity<LeaseQuotationResponseDTO> createLeaseQuotation(
      @Valid @RequestBody LeaseQuotationRequestDTO leaseQuotationRequest) {
    try {
      LeaseQuotation leaseQuotation =
          leaseQuotationObjectFactory.create(leaseQuotationRequest);
      leaseQuotationOutProxyService.leaseQuotationOut(leaseQuotation);

      String quotationNumber = extractQuotationNumber(leaseQuotationRequest);
      LeaseQuotationResponseDTO response = LeaseQuotationResponseDTO.success(quotationNumber);
      return ResponseEntity.ok(response);
    } catch (Exception ex) {
      log.error("Error while creating SAP Lease Quotation ", ex);
      LeaseQuotationResponseDTO errorResponse =
          LeaseQuotationResponseDTO.error("Failed to create Lease Quotation: " + ex.getMessage());
      return ResponseEntity.internalServerError().body(errorResponse);
    }
  }

  private String extractQuotationNumber(LeaseQuotationRequestDTO request) {
    try {
      if (request.getRecord() != null && !request.getRecord().isEmpty() &&
          request.getRecord().get(0).getHeader() != null &&
          request.getRecord().get(0).getHeader().getLeaseQuotationNumber() != null) {
        return request.getRecord().get(0).getHeader().getLeaseQuotationNumber().toString();
      }
    } catch (Exception ex) {
      log.warn("Unable to extract quotation number from request", ex);
    }
    return null;
  }
}
