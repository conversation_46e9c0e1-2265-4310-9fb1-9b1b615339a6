package com.seera.lumi.core.integrations.adapter.sap.dto.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;

@Data
public class LeaseQuotationRequestDTO {

  @Valid
  @NotEmpty(message = "Record list cannot be empty")
  private List<Record> record;

  @Data
  public static class Record {

    @Valid
    @NotNull(message = "Header is required")
    private Header header;

    @Valid
    @NotEmpty(message = "Detail list cannot be empty")
    private List<Detail> detail;
  }

  @Data
  public static class Header {

    @NotNull(message = "Lease quotation number is required")
    private BigInteger leaseQuotationNumber;

    @NotNull(message = "Group number is required")
    private BigInteger groupNumber;

    @NotNull(message = "Serial number is required")
    private BigInteger serialNumber;
  }

  @Data
  public static class Detail {

    private BigInteger make;

    private BigInteger model;

    @Size(max = 4, message = "Version cannot exceed 4 characters")
    private String version;

    private BigInteger series;

    @Size(max = 8, message = "Car group cannot exceed 8 characters")
    private String carGroup;

    @Size(max = 18, message = "Vehicle code cannot exceed 18 characters")
    private String vehicleCode;

    @Size(max = 18, message = "Group accessory code cannot exceed 18 characters")
    private String groupAccessoryCode;

    @Size(max = 18, message = "Accessory code cannot exceed 18 characters")
    private String accessoryCode;

    @Size(max = 1, message = "Accessory type cannot exceed 1 character")
    private String accessoryType;

    @Size(max = 18, message = "Other items code cannot exceed 18 characters")
    private String otherItemsCode;

    private BigDecimal vehiclePrice;

    private BigDecimal accessoryPrice;

    @NotNull(message = "Currency is required")
    @Size(max = 3, message = "Currency cannot exceed 3 characters")
    private String currency;

    @NotNull(message = "Quantity is required")
    private BigDecimal quantity;

    @NotNull(message = "Branch is required")
    @Size(max = 4, message = "Branch cannot exceed 4 characters")
    private String branch;

    @NotNull(message = "Company code is required")
    @Size(max = 4, message = "Company code cannot exceed 4 characters")
    private String companyCode;

    @NotNull(message = "Creation date is required")
    @Size(max = 10, message = "Creation date cannot exceed 10 characters")
    private String creationDate;

    @Size(max = 10, message = "Debitor cannot exceed 10 characters")
    private String debitor;

    @Size(max = 10, message = "Validation date cannot exceed 10 characters")
    private String validationDate;

    @Size(max = 10, message = "Expected start date of lease cannot exceed 10 characters")
    private String expectedStartDateofLease;

    @Size(max = 10, message = "Supplier cannot exceed 10 characters")
    private String supplier;

    @Size(max = 10, message = "Customer cannot exceed 10 characters")
    private String customer;

    private BigInteger durationMonths;

    private BigInteger durationKms;

    @Size(max = 10, message = "Created by cannot exceed 10 characters")
    private String createdBy;

    @Size(max = 10, message = "Requisitioner cannot exceed 10 characters")
    private String requisitioner;

    @Size(max = 4, message = "Model year cannot exceed 4 characters")
    private String modelYear;
  }
}
