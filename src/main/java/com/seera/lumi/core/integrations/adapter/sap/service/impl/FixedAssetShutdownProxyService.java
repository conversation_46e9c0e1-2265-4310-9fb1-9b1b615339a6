package com.seera.lumi.core.integrations.adapter.sap.service.impl;

import static com.seera.lumi.core.integrations.adapter.logging.enums.ExternalServiceName.SAP_ASSET_SHUTDOWN;
import static java.util.Objects.nonNull;

import com.altayyargroup.fin.assetshutdown.FixedAssetShutdown;
import com.altayyargroup.fin.assetshutdown.FixedAssetShutdown_Type;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.seera.lumi.core.integrations.adapter.logging.dto.IntegrationLogEvent;
import com.seera.lumi.core.integrations.adapter.logging.enums.LogEventType;
import com.seera.lumi.core.integrations.adapter.logging.service.IntegrationLogService;
import com.seera.lumi.core.integrations.adapter.util.LogUtil;
import io.micrometer.tracing.Tracer;
import java.net.SocketTimeoutException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.RetryContext;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.retry.support.RetrySynchronizationManager;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class FixedAssetShutdownProxyService {

  private final FixedAssetShutdown fixedAssetShutdown;
  private final IntegrationLogService logService;
  private final ObjectMapper mapper;
  private final Tracer tracer;

  @Retryable(
      retryFor = SocketTimeoutException.class,
      maxAttemptsExpression = "${integration.retry.services.sap.maxRetryAttempts:3}",
      backoff =
          @Backoff(
              delayExpression = "${integration.retry.services.sap.initialBackoffMs:1000}",
              multiplierExpression = "${integration.retry.services.sap.backoffMultiplier:2}",
              maxDelayExpression = "${integration.retry.services.sap.maxBackoffMs:10000}"))
  public void fixedAssetShutdown(FixedAssetShutdown_Type fixedAssetShutdownRequest) {
    String traceId = getTraceId();
    try {
      logRequest(fixedAssetShutdownRequest, traceId);
      fixedAssetShutdown.fixedAssetShutdown(fixedAssetShutdownRequest.getRecord());
      logResponse("SUCCESS", traceId);
    } catch (Exception ex) {
      logResponse(ex.getMessage(), traceId);
      throw ex;
    }
  }

  private String getTraceId() {
    RetryContext retryContext = RetrySynchronizationManager.getContext();
    String traceId = LogUtil.getTraceId(tracer);
    if (nonNull(retryContext) && retryContext.getRetryCount() > 0) {
      traceId = LogUtil.getTraceId(tracer) + "-retry-" + retryContext.getRetryCount();
    }
    return traceId;
  }

  public void logRequest(FixedAssetShutdown_Type fixedAssetShutdownRequest, String traceId) {
    try {
      IntegrationLogEvent event =
          createLogEvent(
              LogEventType.REQUEST,
              traceId,
              mapper.writeValueAsString(fixedAssetShutdownRequest),
              getRefId(fixedAssetShutdownRequest));
      logService.sendLogEvent(SAP_ASSET_SHUTDOWN, event);
    } catch (Exception ex) {
      log.error("Error while logging SAP Fixed Asset Shutdown Request ", ex);
    }
  }

  public void logResponse(String responseBody, String traceId) {
    try {
      IntegrationLogEvent event =
          createLogEvent(LogEventType.RESPONSE, traceId, responseBody, null);
      logService.sendLogEvent(SAP_ASSET_SHUTDOWN, event);
    } catch (Exception ex) {
      log.error("Error while logging SAP Fixed Asset Shutdown Response ", ex);
    }
  }

  private IntegrationLogEvent createLogEvent(
      LogEventType logEventType, String traceId, String logData, String refId) {
    return new IntegrationLogEvent()
        .setLogEventType(logEventType)
        .setApiUrl("http://altayyargroup.com/FIN/AssetShutdown")
        .setMethodName("FixedAssetShutdown")
        .setTraceId(traceId)
        .setLogData(logData)
        .setRefId(refId);
  }

  private String getRefId(FixedAssetShutdown_Type request) {
    try {
      if (request != null && !request.getRecord().isEmpty()) {
        return request.getRecord().get(0).getASSET();
      }
    } catch (Exception ex) {
      log.warn("Unable to extract reference ID from Fixed Asset Shutdown request", ex);
    }
    return null;
  }
}
