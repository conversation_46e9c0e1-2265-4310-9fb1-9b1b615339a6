package com.seera.lumi.core.integrations.adapter.util;

import com.altayyargroup.fin.assetshutdown.FixedAssetShutdown;
import com.altayyargroup.fin.documentposting.DocumentPostingOut;
import com.altayyargroup.fin.fetchlumivehicleinformation.FetchLumiVehicleInformationOut;
import com.altayyargroup.mdm.fetchlumicustomermasterdetails.FetchLumiCustomerMasterDetailsOut;
import com.altayyargroup.mdm.lumicustomermaster.LumiCustomerMasterOut;
import com.altayyargroup.scm.salesorder.SalesOrderOut;
import jakarta.annotation.PostConstruct;
import jakarta.xml.ws.BindingProvider;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

@ConditionalOnProperty(value = "soap.log.enable", havingValue = "true")
@Configuration
@Slf4j
public class SAPSoapLogConfigurer {

  private final FetchLumiCustomerMasterDetailsOut fetchCustomerInfoService;
  private final FetchLumiVehicleInformationOut fetchVehicleInfoService;
  private final LumiCustomerMasterOut customerMasterService;

  private final DocumentPostingOut customerPaymentService;

  private final SalesOrderOut createSalesOrderService;
  private final FixedAssetShutdown fixedAssetShutdownService;
  private final SoapMessageHandler soapMessageHandler;
  private final Map<BindingProvider, Boolean> bindingProviders;

  public SAPSoapLogConfigurer(
      FetchLumiCustomerMasterDetailsOut fetchCustomerInfoService,
      FetchLumiVehicleInformationOut fetchVehicleInfoService,
      LumiCustomerMasterOut customerMasterService,
      DocumentPostingOut customerPaymentService,
      SalesOrderOut createSalesOrderService,
      FixedAssetShutdown fixedAssetShutdownService,
      SoapMessageHandler soapMessageHandler) {
    this.fetchCustomerInfoService = fetchCustomerInfoService;
    this.fetchVehicleInfoService = fetchVehicleInfoService;
    this.customerMasterService = customerMasterService;
    this.customerPaymentService = customerPaymentService;
    this.createSalesOrderService = createSalesOrderService;
    this.fixedAssetShutdownService = fixedAssetShutdownService;
    this.soapMessageHandler = soapMessageHandler;
    this.bindingProviders = new HashMap<>();
    this.bindingProviders.put((BindingProvider) this.fetchCustomerInfoService, true);
    this.bindingProviders.put((BindingProvider) this.fetchVehicleInfoService, true);
    this.bindingProviders.put((BindingProvider) this.customerMasterService, true);
    this.bindingProviders.put((BindingProvider) this.customerPaymentService, true);
    this.bindingProviders.put((BindingProvider) this.createSalesOrderService, true);
    this.bindingProviders.put((BindingProvider) this.fixedAssetShutdownService, true);
  }

  @PostConstruct
  public void configure() {
    bindingProviders.forEach(
        (bindingProvider, shouldLog) -> {
          if (Boolean.TRUE.equals(shouldLog)) {
            var soapHandlers = new ArrayList<>(bindingProvider.getBinding().getHandlerChain());
            soapHandlers.add(soapMessageHandler);
            log.debug("Added soap log handler for service: {}", bindingProvider);
            bindingProvider.getBinding().setHandlerChain(soapHandlers);
          }
        });
  }
}
