## External System Configuration
sap:
  qa.host: "https://sapqasdp.seera.sa:443"
  senderParty: "${SAP_SENDER_PARTY:}"
  senderService: "${SAP_SENDER_SERVICE:LUMIRental}"
  receiverParty: "${SAP_RECEIVER_PARTY:}"
  receiverService: "${SAP_RECEIVER_SERVICE:ECQCLNT340}"

integration:
  client:
    sap:
      host: "${SAP_HOST:${sap.qa.host}}"
      url:
        path: "${integration.client.sap.host}/XISOAPAdapter/MessageServlet"
      interfaces:
        fetchVehicleInfo: "${SAP_FETCH_VEHICLE_INFO_ENDPOINT:${integration.client.sap.url.path}?senderParty=${sap.senderParty}&senderService=${sap.senderService}&receiverParty=${sap.receiverParty}&receiverService=${sap.receiverService}&interface=FetchLumiVehicleInformation_Out&interfaceNamespace=http://altayyargroup.com/FIN/FetchLumiVehicleInformation}"
        fetchCustomerInfo: "${SAP_FETCH_CUSTOMER_INFO_ENDPOINT:${integration.client.sap.url.path}?senderParty=${sap.senderParty}&senderService=${sap.senderService}&receiverParty=${sap.receiverParty}&receiverService=${sap.receiverService}&interface=FetchLumiCustomerMasterDetails_Out&interfaceNamespace=http://altayyargroup.com/MDM/FetchLumiCustomerMasterDetails}"
        createCustomer: "${SAP_CREATE_CUSTOMER_ENDPOINT:${integration.client.sap.url.path}?senderParty=${sap.senderParty}&senderService=${sap.senderService}&receiverParty=${sap.receiverParty}&receiverService=${sap.receiverService}&interface=LumiCustomerMaster_Out&interfaceNamespace=http://altayyargroup.com/MDM/LumiCustomerMaster}"
        createCustomerPayment: "${SAP_CREATE_CUSTOMER_PAYMENT_ENDPOINT:${integration.client.sap.url.path}?senderParty=${sap.senderParty}&senderService=${sap.senderService}&receiverParty=${sap.receiverParty}&receiverService=${sap.receiverService}&interface=DocumentPosting_Out&interfaceNamespace=http://altayyargroup.com/FIN/DocumentPosting}"
        createSalesOrder: "${SAP_CREATE_SALES_ORDER_ENDPOINT:${integration.client.sap.url.path}?senderParty=${sap.senderParty}&senderService=${sap.senderService}&receiverParty=${sap.receiverParty}&receiverService=${sap.receiverService}&interface=SalesOrder_Out&interfaceNamespace=http://altayyargroup.com/SCM/SalesOrder}"
        fixedAssetShutdown: "${SAP_FIXED_ASSET_SHUTDOWN_ENDPOINT:${integration.client.sap.url.path}?senderParty=${sap.senderParty}&senderService=${sap.senderService}&receiverParty=${sap.receiverParty}&receiverService=${sap.receiverService}&interface=FixedAssetShutdown&interfaceNamespace=http://altayyargroup.com/FIN/AssetShutdown}"
        leaseQuotation: "${SAP_LEASE_QUOTATION_ENDPOINT:${integration.client.sap.url.path}?senderParty=${sap.senderParty}&senderService=${sap.senderService}&receiverParty=${sap.receiverParty}&receiverService=${sap.receiverService}&interface=LeaseQuotation_Out&interfaceNamespace=http://altayyargroup.com/SCM/LeaseQuotation}"
      username: ${SAP_USERNAME:PIAPPLLUMI}
      password: ${SAP_PASSWORD:A1Tayyar123$}
    tamm:
      webApiUrl: ${TAMM_WEB_API_URL:}
      apiUrl: ${TAMM_API_URL:}
      idpUrl: ${TAMM_IDP_URL:}
      integrationUserId: ${TAMM_INTEGRATION_USER_ID:}
      enable-authentication-logging: ${TAMM_ENABLE_AUTHENTICATION_LOGGING:false}
    tajeer:
      url: ${TAJEER_URL:}
      app-id: ${TAJEER_APP_ID:}
      app-key: ${TAJEER_APP_KEY:}
      user-name: ${TAJEER_APP_USERNAME:}
      password: ${TAJEER_APP_PASSWORD:}
