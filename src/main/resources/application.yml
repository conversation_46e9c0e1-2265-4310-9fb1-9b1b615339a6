info:
  app:
    description: An Adapter service for the integrations with external Systems.
    version: ${BUILD_REVISION:1.0.0}

## Server Config
server:
  compression:
    enabled: true
    mime-types: text/html, text/xml, text/plain, text/css, text/javascript, application/javascript,
      application/json
  port: ${SERVICE_PORT:${SERVER_PORT:8097}}

## External System Configuration
sap:
  qa.host: "https://sapqasdp.seera.sa:443"
  senderParty: "${SAP_SENDER_PARTY:}"
  senderService: "${SAP_SENDER_SERVICE:LUMIRental}"
  receiverParty: "${SAP_RECEIVER_PARTY:}"
  receiverService: "${SAP_RECEIVER_SERVICE:ECQCLNT340}"

integration:
  # Retry configuration for external service calls
  retry:
    # Default maximum number of retry attempts
    maxRetryAttempts: 3
    services:
      sap:
        maxRetryAttempts: 3
        initialBackoffMs: 1000
        maxBackoffMs: 10000
        backoffMultiplier: 2.0
  client:
    sap:
      host: "${SAP_HOST:${sap.qa.host}}"
      url:
        path: "${integration.client.sap.host}/XISOAPAdapter/MessageServlet"
      interfaces:
        fetchVehicleInfo: "${SAP_FETCH_VEHICLE_INFO_ENDPOINT:${integration.client.sap.url.path}?senderParty=${sap.senderParty}&senderService=${sap.senderService}&receiverParty=${sap.receiverParty}&receiverService=${sap.receiverService}&interface=FetchLumiVehicleInformation_Out&interfaceNamespace=http://altayyargroup.com/FIN/FetchLumiVehicleInformation}"
        fetchCustomerInfo: "${SAP_FETCH_CUSTOMER_INFO_ENDPOINT:${integration.client.sap.url.path}?senderParty=${sap.senderParty}&senderService=${sap.senderService}&receiverParty=${sap.receiverParty}&receiverService=${sap.receiverService}&interface=FetchLumiCustomerMasterDetails_Out&interfaceNamespace=http://altayyargroup.com/MDM/FetchLumiCustomerMasterDetails}"
        createCustomer: "${SAP_CREATE_CUSTOMER_ENDPOINT:${integration.client.sap.url.path}?senderParty=${sap.senderParty}&senderService=${sap.senderService}&receiverParty=${sap.receiverParty}&receiverService=${sap.receiverService}&interface=LumiCustomerMaster_Out&interfaceNamespace=http://altayyargroup.com/MDM/LumiCustomerMaster}"
        createCustomerPayment: "${SAP_CREATE_CUSTOMER_PAYMENT_ENDPOINT:${integration.client.sap.url.path}?senderParty=${sap.senderParty}&senderService=${sap.senderService}&receiverParty=${sap.receiverParty}&receiverService=${sap.receiverService}&interface=DocumentPosting_Out&interfaceNamespace=http://altayyargroup.com/FIN/DocumentPosting}"
        createSalesOrder: "${SAP_CREATE_SALES_ORDER_ENDPOINT:${integration.client.sap.url.path}?senderParty=${sap.senderParty}&senderService=${sap.senderService}&receiverParty=${sap.receiverParty}&receiverService=${sap.receiverService}&interface=SalesOrder_Out&interfaceNamespace=http://altayyargroup.com/SCM/SalesOrder}"
        fixedAssetShutdown: "${SAP_FIXED_ASSET_SHUTDOWN_ENDPOINT:${integration.client.sap.url.path}?senderParty=${sap.senderParty}&senderService=${sap.senderService}&receiverParty=${sap.receiverParty}&receiverService=${sap.receiverService}&interface=FixedAssetShutdown&interfaceNamespace=http://altayyargroup.com/FIN/AssetShutdown}"
        leaseQuotation: "${SAP_LEASE_QUOTATION_ENDPOINT:${integration.client.sap.url.path}?senderParty=${sap.senderParty}&senderService=${sap.senderService}&receiverParty=${sap.receiverParty}&receiverService=${sap.receiverService}&interface=LeaseQuotation_Out&interfaceNamespace=http://altayyargroup.com/SCM/LeaseQuotation}"
      username: ${SAP_USERNAME:}
      password: ${SAP_PASSWORD:}

    tamm:
      webApiUrl: ${TAMM_WEB_API_URL:}
      apiUrl: ${TAMM_API_URL:}
      idpUrl: ${TAMM_IDP_URL:}
      integrationUserId: ${TAMM_INTEGRATION_USER_ID:}
      enable-authentication-logging: ${TAMM_ENABLE_AUTHENTICATION_LOGGING:false}
      maxRetryAttempts: 3
    tajeer:
      url: ${TAJEER_URL:}
      app-id: ${TAJEER_APP_ID:}
      app-key: ${TAJEER_APP_KEY:}
      user-name: ${TAJEER_APP_USERNAME:}
      password: ${TAJEER_APP_PASSWORD:}
      maxRetryAttempts: 3
      defaultConfig:
        operatorId: ${TAJEER_OPERATOR_ID:1028558326}
        rentalPolicyId: ${TAJEER_RENTAL_POLICY_ID:10184}
        authorizationTypeId: ${TAJEER_AUTHORIZATION_TYPE_ID:1}
        contractTypeId: ${TAJEER_CONTRACT_TYPE_ID:1}
        allowedLateHour: ${TAJEER_ALLOWED_LATE_HOUR:2}
        extendedCoverageId: ${TAJEER_EXTENDED_COVERAGE_ID:431}

kafka:
  dlt:
    listen:
      auto:
        start: ${DLT_AUTO_START:true}
  listen:
    auto:
      start: true
    concurrency: ${KAFKA_LISTENER_CONCURRENCY:1}
  topic:
    sap:
      payment:
        notification:
          event: SAPPaymentNotificationEvent
      ingested:
        data:
          event: sap.ingested.data.event
    integration:
      log:
        data: integration.log.data
##
logging:
  level:
    root: info
    com:
      seera:
        lumi: DEBUG
    org:
      apache:
        kafka: WARN
      springframework:
        kafka: WARN
    kafka: WARN
    io:
      confluent: WARN
    org.apache.kafka.common.utils.AppInfoParser: ERROR

management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus
security:
  basic:
    enable: false
soap:
  log:
    enable: ${SOAP_LOGGING:true}

## Spring Specific Configuration
spring:
  application:
    name: integrations-adapter-service
  cloud:
    micrometer:
      enabled: true
    stream:
      kafka:
        binder:
          minPartitionCount: ${KAFKA_PARTITION:1}
          replicationFactor: ${KAFKA_REPLICATION:1}
    openfeign:
      httpclient:
        connection-timeout: 20000
        ok-http:
          read-timeout: 20s
        max-connections: 250
      okhttp:
        enabled: 'true'
      client:
        config:
          default:
            connect-timeout: 20000
            read-timeout: 20000
            micrometer:
              enabled: true
  kafka:
    bootstrap-servers: ${KAFKA_HOST:localhost:9092}
    consumer:
      auto-offset-reset: latest
      group-id: ${spring.application.name}
      properties:
        max:
          poll:
            interval:
              ms: 3600000
        request:
          timeout:
            ms: 300000
  profiles:
    active: ${ACTIVE_PROFILE:local}
  data:
    redis:
      host: ${SPRING_REDIS_HOST:localhost}
      password: ${SPRING_REDIS_PASSWORD:}
      port: ${SPRING_REDIS_PORT:6379}
      timeout: '60'
      ssl:
        enabled: ${SPRING_REDIS_SSL:false}
      database: 6
  ## Spring Security Oauth2 Client Config
  security:
    oauth2:
      client:
        registration:
          TAMM-CLIENT:
            provider: TAMM-IDP
            client-id: ${TAMM_CLIENT_ID:}
            client-secret: ${TAMM_CLIENT_SECRET:}
            scope: [ openid, profile ]
            authorization-grant-type: client_credentials
        provider:
          TAMM-IDP:
            token-uri: ${integration.client.tamm.idpUrl}/token
            authorization-uri: ${integration.client.tamm.idpUrl}/auth
            user-info-uri: ${integration.client.tamm.idpUrl}/userinfo
            jwk-set-uri: ${integration.client.tamm.idpUrl}/certs
            user-name-attribute: preferred_username

## Swagger Configs
springdoc:
  swagger-ui:
    host: ${SWAGGER_HOST:}
    path: /swagger-ui.html
swagger:
  context: ${SWAGGER_CONTEXT:}
  contact:
    url: https://lumirental.com
    email: <EMAIL>
  security:
    option:
      enable: false

## Security Config
config:
  multitenant:
    policy-enforcer:
      enable: false
    tenants:
      lumi:
        keycloak-config:
          config-path: ${LUMI_KEYCLOAK_CONFIG_PATH:src/main/resources/keycloak-lumi.json}
          manager-username: ${LUMI_KEYCLOAK_MANAGER_USERNAME:ucs}
          manager-password: ${LUMI_KEYCLOAK_MANAGER_PASSWORD:123456}
        tenant-identifier: LUMI
        token-converter-properties:
          resource-id: account
          principal-attribute: preferred_username
        jwk-set-uri: ${config.multitenant.tenants.lumi.issuer-uri}/protocol/openid-connect/certs
        issuer-uri: ${KEYCLOAK_URL:https://keycloak.dev.lumirental.com}/realms/lumi
    default-tenant-identifier: LUMI